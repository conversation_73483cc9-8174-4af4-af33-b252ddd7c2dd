package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.genco.common.model.product.StoreProductShareRecord;
import com.genco.service.dao.StoreProductShareRecordDao;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.StoreProductShareRecordService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class StoreProductShareRecordServiceImpl implements StoreProductShareRecordService {
    @Autowired
    private StoreProductShareRecordDao storeProductShareRecordDao;

    @Autowired
    private StoreProductService storeProductService;

    /**
     * 异步保存商品分享转链记录
     * @param record 记录实体
     */
    @Async
    @Override
    public void saveAsync(StoreProductShareRecord record) {
        storeProductShareRecordDao.insert(record);
    }

    /**
     * 同步保存商品分享转链记录
     * @param record 记录实体
     */
    @Override
    public void save(StoreProductShareRecord record) {
        storeProductShareRecordDao.insert(record);
    }

    /**
     * 分页查询商品分享转链记录（支持多条件搜索）
     * @param request 查询参数（用户ID、昵称、商品ID、商品名、渠道、时间区间等）
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    @Override
    public PageInfo<StoreProductShareRecord> getAdminList(com.genco.common.request.StoreProductShareRecordSearchRequest request, com.genco.common.request.PageParamRequest pageParamRequest) {
        LambdaQueryWrapper<StoreProductShareRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 关键词搜索 - 支持用户昵称、商品名称、商品ID的模糊搜索
        if (StringUtils.isNotBlank(request.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                .like(StoreProductShareRecord::getProductName, request.getKeyword())
            );
        }

        // 品牌筛选 - 通过StoreProductService获取商品ID列表，避免SQL注入
        if (StringUtils.isNotBlank(request.getBrandCode())) {
            List<String> outProductIds = storeProductService.getOutProductIdsByBrandCode(request.getBrandCode());
            if (!CollectionUtils.isEmpty(outProductIds)) {
                queryWrapper.in(StoreProductShareRecord::getProductId, outProductIds);
            } else {
                // 如果没有找到对应品牌的商品，返回空结果
                queryWrapper.eq(StoreProductShareRecord::getId, -1);
            }
        }

        // 精确搜索
        if (request.getUserId() != null) {
            queryWrapper.eq(StoreProductShareRecord::getUserId, request.getUserId());
        }
        if (StringUtils.isNotBlank(request.getTiktokUid())) {
            queryWrapper.eq(StoreProductShareRecord::getTiktokUid, request.getTiktokUid());
        }
        if (StringUtils.isNotBlank(request.getProductId())) {
            queryWrapper.eq(StoreProductShareRecord::getProductId, request.getProductId());
        }
        if (StringUtils.isNotBlank(request.getChannel())) {
            queryWrapper.eq(StoreProductShareRecord::getChannel, request.getChannel());
        }
        // 模糊搜索
        if (StringUtils.isNotBlank(request.getUserAccount())) {
            queryWrapper.like(StoreProductShareRecord::getUserAccount, request.getUserAccount());
        }
        if (StringUtils.isNotBlank(request.getProductName())) {
            queryWrapper.like(StoreProductShareRecord::getProductName, request.getProductName());
        }
        // 时间区间
        if (StringUtils.isNotBlank(request.getOperateTimeStart())) {
            queryWrapper.ge(StoreProductShareRecord::getOperateTime, request.getOperateTimeStart());
        }
        if (StringUtils.isNotBlank(request.getOperateTimeEnd())) {
            queryWrapper.le(StoreProductShareRecord::getOperateTime, request.getOperateTimeEnd());
        }
        queryWrapper.orderByDesc(StoreProductShareRecord::getOperateTime);
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        java.util.List<StoreProductShareRecord> list = storeProductShareRecordDao.selectList(queryWrapper);
        return new PageInfo<>(list);
    }
} 