package com.genco.service;

import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreProductShareRecordSearchRequest;
import com.genco.service.service.StoreProductShareRecordService;
import com.github.pagehelper.PageInfo;
import com.genco.common.model.product.StoreProductShareRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 商品分享转链记录服务测试类
 * 用于验证前后端字段语义差异修复后的功能
 */
@SpringBootTest
public class StoreProductShareRecordServiceTest {

    @Autowired
    private StoreProductShareRecordService storeProductShareRecordService;

    /**
     * 测试关键词搜索功能
     */
    @Test
    public void testKeywordSearch() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setKeyword("test");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("关键词搜索结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试品牌筛选功能
     */
    @Test
    public void testBrandCodeFilter() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setBrandCode("BRAND001");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("品牌筛选结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试组合搜索功能
     */
    @Test
    public void testCombinedSearch() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setKeyword("商品");
        request.setBrandCode("BRAND001");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("组合搜索结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试空参数查询
     */
    @Test
    public void testEmptySearch() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("空参数查询结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }
}
